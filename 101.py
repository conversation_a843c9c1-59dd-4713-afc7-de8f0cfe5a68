import math

def grpo_objective(rhos, A, pi_theta_old, pi_theta_ref, epsilon=0.2, beta=0.01) -> float:
	"""
    Implement the GRPO (Generalized Relative Policy Optimization) objective function used to optimize policy parameters in reinforcement learning. Your task is to compute the GRPO objective given the likelihood ratios, advantage estimates, old policy probabilities, reference policy probabilities, and apply the clipping mechanism and KL divergence penalty correctly to maintain training stability.
    
    Example:
    Input:
    grpo_objective([1.2, 0.8, 1.1], [1.0, 1.0, 1.0], [0.9, 1.1, 1.0], [1.0, 0.5, 1.5], epsilon=0.2, beta=0.01)

    Output:
    1.032749 (Note: The exact expected value may use slightly different rounding)

    Reasoning:
    The function calculates the GRPO objective by first clipping the likelihood ratios, computing the minimum terms, averaging them, and then subtracting the KL divergence penalty scaled by beta.

	Compute the GRPO objective function.

	Args:
		rhos: List of likelihood ratios (p_i) = pi_theta(o_i | q) / pi_theta_old(o_i | q).
		A: List of advantage estimates (A_i).
		pi_theta_old: List representing the old policy probabilities pi_theta_old(o_i | q).
		pi_theta_ref: List representing the reference policy probabilities pi_ref(o_i | q).
		epsilon: Clipping parameter (eps).
		beta: KL divergence penalty coefficient (beta).

	Returns:
		The computed GRPO objective value.
	"""
	# Step 1: Clip the likelihood ratios to [1-epsilon, 1+epsilon]
	clipped_rhos = [max(1 - epsilon, min(1 + epsilon, rho)) for rho in rhos]
	
	# Step 2: Compute the minimum between clipped and unclipped ratios multiplied by advantages
	min_terms = [min(rho * a, clipped_rho * a) 
				 for rho, clipped_rho, a in zip(rhos, clipped_rhos, A)]
	
	# Step 3: Compute the average of minimum terms
	avg_min_terms = sum(min_terms) / len(min_terms)
	
	# Step 4: Compute KL divergence between old policy and reference policy
	kl_div = 0.0
	for old_prob, ref_prob in zip(pi_theta_old, pi_theta_ref):
		if ref_prob > 0 and old_prob > 0:
			kl_div += old_prob * math.log(old_prob / ref_prob)
	
	# Step 5: Compute final GRPO objective with KL penalty
	grpo_objective_value = avg_min_terms - beta * kl_div
	
	return float(grpo_objective_value)